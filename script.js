// Hero section animations
anime({
  targets: '#name',
  translateY: [-50, 0],
  opacity: [0, 1],
  duration: 1500,
  easing: 'easeOutExpo'
});

anime({
  targets: '#tagline',
  translateY: [-30, 0],
  opacity: [0, 1],
  delay: 500,
  duration: 1200,
  easing: 'easeOutExpo'
});

// Navigation animation
anime({
  targets: 'nav a',
  translateY: [-20, 0],
  opacity: [0, 1],
  delay: anime.stagger(100, {start: 800}),
  duration: 800,
  easing: 'easeOutExpo'
});

// Scroll progress indicator
function updateScrollProgress() {
  const scrollTop = window.pageYOffset;
  const docHeight = document.body.scrollHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;
  
  const progressBar = document.querySelector('.scroll-progress');
  if (progressBar) {
    progressBar.style.width = scrollPercent + '%';
  }
}

// Create scroll progress indicator
function createScrollIndicator() {
  const indicator = document.createElement('div');
  indicator.className = 'scroll-indicator';
  indicator.innerHTML = '<div class="scroll-progress"></div>';
  document.body.appendChild(indicator);
}

// Intersection Observer for scroll animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const target = entry.target;
      
      // Section headers animation
      if (target.tagName === 'H2') {
        anime({
          targets: target,
          translateY: [30, 0],
          opacity: [0, 1],
          duration: 800,
          easing: 'easeOutExpo'
        });
      }
      
      // Skills animation
      if (target.id === 'skills') {
        anime({
          targets: '#skills li',
          translateY: [50, 0],
          opacity: [0, 1],
          delay: anime.stagger(100),
          duration: 800,
          easing: 'easeOutExpo'
        });
      }
      
      // Projects animation
      if (target.classList.contains('project-card')) {
        anime({
          targets: target,
          translateY: [50, 0],
          opacity: [0, 1],
          scale: [0.9, 1],
          duration: 800,
          easing: 'easeOutExpo'
        });
      }
      
      // About section animation
      if (target.id === 'about') {
        anime({
          targets: '#about p',
          translateY: [30, 0],
          opacity: [0, 1],
          duration: 800,
          delay: 200,
          easing: 'easeOutExpo'
        });
      }
      
      // Contact section animation
      if (target.id === 'contact') {
        anime({
          targets: '.contact-links a',
          translateY: [30, 0],
          opacity: [0, 1],
          delay: anime.stagger(100, {start: 200}),
          duration: 600,
          easing: 'easeOutExpo'
        });
      }
    }
  });
}, observerOptions);

// Tech tags hover animation
function animateTechTags() {
  const techTags = document.querySelectorAll('.tech-tag');
  techTags.forEach(tag => {
    tag.addEventListener('mouseenter', () => {
      anime({
        targets: tag,
        scale: 1.1,
        duration: 200,
        easing: 'easeOutQuad'
      });
    });
    
    tag.addEventListener('mouseleave', () => {
      anime({
        targets: tag,
        scale: 1,
        duration: 200,
        easing: 'easeOutQuad'
      });
    });
  });
}

// Smooth scroll for navigation links
function setupSmoothScroll() {
  const navLinks = document.querySelectorAll('nav a[href^="#"]');
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const targetId = link.getAttribute('href');
      const targetSection = document.querySelector(targetId);
      
      if (targetSection) {
        // Animate scroll
        anime({
          targets: 'html, body',
          scrollTop: targetSection.offsetTop - 80,
          duration: 800,
          easing: 'easeInOutQuad'
        });
        
        // Add active state animation
        anime({
          targets: link,
          scale: [1, 1.1, 1],
          duration: 300,
          easing: 'easeOutQuad'
        });
      }
    });
  });
}

// Floating animation for project cards
function setupProjectCardAnimations() {
  const projectCards = document.querySelectorAll('.project-card');
  projectCards.forEach((card, index) => {
    // Subtle floating animation
    anime({
      targets: card,
      translateY: [-5, 5],
      duration: 2000 + (index * 200),
      direction: 'alternate',
      loop: true,
      easing: 'easeInOutSine'
    });
  });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Create scroll indicator
  createScrollIndicator();
  
  // Set up observers for all sections
  const sections = document.querySelectorAll('section');
  const headers = document.querySelectorAll('section h2');
  const projectCards = document.querySelectorAll('.project-card');
  
  sections.forEach(section => observer.observe(section));
  headers.forEach(header => observer.observe(header));
  projectCards.forEach(card => observer.observe(card));
  
  // Initialize interactive features
  animateTechTags();
  setupSmoothScroll();
  
  // Add floating animation after a delay
  setTimeout(setupProjectCardAnimations, 2000);
  
  // Update scroll progress
  window.addEventListener('scroll', updateScrollProgress);
  
  // Initial scroll progress update
  updateScrollProgress();
});

// Add some interactive cursor effects (optional enhancement)
// Category filter buttons functionality for Library section
document.addEventListener('DOMContentLoaded', () => {
  const categoryButtons = document.querySelectorAll('.category-btn');
  const libraryItems = document.querySelectorAll('.library-item');
  const modal = document.getElementById('imageModal');
  const modalImg = document.getElementById('modalImage');
  const captionText = document.getElementById('caption');
  const closeBtn = modal.querySelector('.close');

  categoryButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons
      categoryButtons.forEach(btn => btn.classList.remove('active'));
      // Add active class to clicked button
      button.classList.add('active');

      const category = button.getAttribute('data-category');

      // Show popup of featured certificate for selected category
      const modal = document.getElementById('imageModal');
      const modalImg = document.getElementById('modalImage');
      const captionText = document.getElementById('caption');

      let featuredImageSrc = '';
      let featuredImageAlt = '';

      if (category === 'projects') {
        // For School Management System project, show multiple images popup
        const eventOrganizerImages = [
          'assets/images/event-organizer/image1.jpg',
          'assets/images/event-organizer/image2.jpg',
          'assets/images/event-organizer/image3.jpg'
        ];

        let currentIndex = 0;

        function showImage(index) {
          modalImg.src = eventOrganizerImages[index];
          captionText.textContent = `School Management System Image ${index + 1} of ${eventOrganizerImages.length}`;
        }

        showImage(currentIndex);
        modal.style.display = 'block';

        // Create navigation buttons
        let nextBtn = document.getElementById('nextBtn');
        let prevBtn = document.getElementById('prevBtn');

        if (!nextBtn) {
          nextBtn = document.createElement('button');
          nextBtn.id = 'nextBtn';
          nextBtn.textContent = 'Next';
          nextBtn.style.position = 'absolute';
          nextBtn.style.top = '50%';
          nextBtn.style.right = '10px';
          nextBtn.style.transform = 'translateY(-50%)';
          nextBtn.style.padding = '10px';
          nextBtn.style.background = 'rgba(0,0,0,0.5)';
          nextBtn.style.color = '#fff';
          nextBtn.style.border = 'none';
          nextBtn.style.cursor = 'pointer';
          modal.appendChild(nextBtn);
        }

        if (!prevBtn) {
          prevBtn = document.createElement('button');
          prevBtn.id = 'prevBtn';
          prevBtn.textContent = 'Prev';
          prevBtn.style.position = 'absolute';
          prevBtn.style.top = '50%';
          prevBtn.style.left = '10px';
          prevBtn.style.transform = 'translateY(-50%)';
          prevBtn.style.padding = '10px';
          prevBtn.style.background = 'rgba(0,0,0,0.5)';
          prevBtn.style.color = '#fff';
          prevBtn.style.border = 'none';
          prevBtn.style.cursor = 'pointer';
          modal.appendChild(prevBtn);
        }

        nextBtn.onclick = () => {
          currentIndex = (currentIndex + 1) % eventOrganizerImages.length;
          showImage(currentIndex);
        };

        prevBtn.onclick = () => {
          currentIndex = (currentIndex - 1 + eventOrganizerImages.length) % eventOrganizerImages.length;
          showImage(currentIndex);
        };

        const closeBtn = modal.querySelector('.close');
        closeBtn.onclick = () => {
          modal.style.display = 'none';
          if (nextBtn) nextBtn.remove();
          if (prevBtn) prevBtn.remove();
        };

        modal.onclick = (e) => {
          if (e.target === modal) {
            modal.style.display = 'none';
            if (nextBtn) nextBtn.remove();
            if (prevBtn) prevBtn.remove();
          }
        };

        return;
      } else if (category === 'hackathons') {
        featuredImageSrc = 'assets/images/hackathons/adobe-india-hackathon-2025.jpg';
        featuredImageAlt = 'Adobe India Hackathon 2025 Certificate';
      } else if (category === 'internships') {
        featuredImageSrc = 'assets/images/internships/iot-robotics-raspberrypi.jpg';
        featuredImageAlt = 'IoRT Robotics & IoT Internship Certificate';
      } else {
        modal.style.display = 'none';
        return;
      }

      modal.style.display = 'block';
      modalImg.src = featuredImageSrc;
      captionText.textContent = featuredImageAlt;

      // Hide all library items since Library section is removed
      const libraryItems = document.querySelectorAll('.library-item');
      libraryItems.forEach(item => {
        item.style.display = 'none';
      });
    });
  });

  // Image popup modal functionality
  libraryItems.forEach(item => {
    const img = item.querySelector('img');
    img.style.cursor = 'pointer';
    img.addEventListener('click', () => {
      modal.style.display = 'block';
      modalImg.src = img.src;
      captionText.textContent = img.alt;
    });
  });

  // Close modal on close button click
  closeBtn.addEventListener('click', () => {
    modal.style.display = 'none';
  });

  // Close modal on clicking outside the image
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });
});

// Existing cursor effect code
document.addEventListener('mousemove', (e) => {
  const cursor = document.querySelector('.cursor');
  if (!cursor) {
    const newCursor = document.createElement('div');
    newCursor.className = 'cursor';
    newCursor.style.cssText = `
      position: fixed;
      width: 20px;
      height: 20px;
      background: rgba(0, 173, 181, 0.3);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      transition: transform 0.1s ease;
    `;
    document.body.appendChild(newCursor);
  }
  
  const cursorElement = document.querySelector('.cursor');
  if (cursorElement) {
    cursorElement.style.left = e.clientX - 10 + 'px';
    cursorElement.style.top = e.clientY - 10 + 'px';
  }
});

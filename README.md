# Portfolio Website

A modern, responsive portfolio website built with HTML5, CSS3, JavaScript, and Anime.js animations.

## Features

- **Responsive Design**: Works seamlessly across desktop, tablet, and mobile devices
- **Smooth Animations**: Powered by Anime.js for engaging user interactions
- **Modern UI**: Clean, professional design with a dark theme
- **Interactive Elements**: Hover effects, scroll animations, and smooth navigation
- **Project Showcase**: Grid layout for displaying work samples
- **Contact Section**: Easy-to-find contact information and social links

## File Structure

```
portfolio/
├── index.html          # Main HTML file
├── style.css           # Responsive CSS styles
├── script.js           # JavaScript with Anime.js animations
├── assets/
│   └── images/
│       ├── projects/   # Project screenshots
│       └── profile/    # Profile images
└── README.md           # This file
```

## Technologies Used

- HTML5
- CSS3 (Grid, Flexbox, Media Queries)
- JavaScript (ES6+)
- Anime.js (Animation library)

## Responsive Breakpoints

- Desktop: 1200px and above
- Tablet: 768px - 1199px
- Mobile: 480px - 767px
- Small Mobile: Below 480px

## Animation Features

- Hero section entrance animations
- Scroll-triggered section animations
- Interactive hover effects
- Smooth scrolling navigation
- Scroll progress indicator
- Floating project cards
- Tech tag interactions

## Setup Instructions

1. Clone or download the project files
2. Open `index.html` in a web browser
3. Replace placeholder images in `assets/images/` with your actual project screenshots
4. Update contact information and project links
5. Customize colors and content as needed

## Customization

- **Colors**: Modify the CSS custom properties for the color scheme
- **Content**: Update the HTML content with your information
- **Images**: Add your project screenshots to the assets folder
- **Animations**: Adjust timing and effects in script.js

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## Performance

- Optimized CSS with efficient selectors
- Minimal JavaScript footprint
- Smooth 60fps animations
- Mobile-first responsive design

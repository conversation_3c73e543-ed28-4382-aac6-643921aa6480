* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #121212;
  color: #f0f0f0;
  line-height: 1.6;
  overflow-x: hidden;
}

header.hero {
  text-align: center;
  padding: 40px 20px;
  background-image: url('assets/images/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

header.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(18, 18, 18, 0.7); /* dark overlay for readability */
  z-index: 0;
}

header.hero > * {
  position: relative;
  z-index: 1;
}

.profile-picture {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #00adb5;
  box-shadow: 0 0 30px rgba(0, 173, 181, 0.4);
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.profile-picture:hover {
  transform: scale(1.05);
  box-shadow: 0 0 40px rgba(0, 173, 181, 0.6);
}

#name {
  font-size: clamp(2rem, 8vw, 4rem);
  margin-bottom: 0.5rem;
  color: #00adb5;
  font-weight: 700;
  text-shadow: 0 0 20px rgba(0, 173, 181, 0.3);
}

#tagline {
  font-size: clamp(1rem, 3vw, 1.4rem);
  color: #ccc;
  margin-bottom: 2rem;
  max-width: 600px;
  text-align: center;
}

nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin-top: 2rem;
}

nav a {
  color: #00adb5;
  text-decoration: none;
  font-weight: bold;
  padding: 10px 20px;
  border: 2px solid transparent;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

nav a:hover {
  border-color: #00adb5;
  background: rgba(0, 173, 181, 0.1);
  transform: translateY(-2px);
}

section {
  padding: 60px 20px;
  max-width: 1200px;
  margin: auto;
}

section h2 {
  font-size: clamp(1.8rem, 5vw, 2.5rem);
  color: #00adb5;
  margin-bottom: 2rem;
  text-align: center;
  position: relative;
}

section h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: #00adb5;
  border-radius: 2px;
}

#about p {
  font-size: 1.1rem;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  color: #ddd;
}

#skills ul {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

#skills li {
  background: #1e1e1e;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #00adb5;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#skills li:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 173, 181, 0.2);
}

#projects {
  background: #0f0f0f;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 2rem;
}

.project-card {
  background: #1e1e1e;
  padding: 30px;
  border-radius: 15px;
  border-left: 4px solid #00adb5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #00adb5, #00d4aa);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.project-card:hover::before {
  transform: scaleX(1);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 173, 181, 0.2);
}

.project-card h3 {
  color: #00adb5;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.project-card p {
  color: #ccc;
  margin-bottom: 1.5rem;
  line-height: 1.7;
}

.project-card a {
  color: #00adb5;
  text-decoration: none;
  font-weight: bold;
  padding: 10px 20px;
  border: 2px solid #00adb5;
  border-radius: 25px;
  display: inline-block;
  transition: all 0.3s ease;
}

.project-card a:hover {
  background: #00adb5;
  color: #121212;
  transform: scale(1.05);
}

#contact {
  text-align: center;
  background: #1f1f1f;
}

#contact p {
  font-size: 1.1rem;
  margin: 1rem 0;
  color: #ddd;
}

.contact-links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 2rem;
}

.contact-links a {
  color: #00adb5;
  text-decoration: none;
  font-weight: bold;
  padding: 15px 30px;
  border: 2px solid #00adb5;
  border-radius: 30px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
}

.contact-links a:hover {
  background: #00adb5;
  color: #121212;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 173, 181, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  header.hero {
    padding: 30px 15px;
    min-height: 90vh;
  }

  .profile-picture {
    width: 120px;
    height: 120px;
    margin-bottom: 1rem;
  }
  
  nav {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  nav a {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
  
  section {
    padding: 40px 15px;
  }
  
  #skills ul {
    grid-template-columns: 1fr;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .project-card {
    padding: 20px;
  }
  
  .contact-links {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  header.hero {
    padding: 20px 10px;
  }

  .profile-picture {
    width: 100px;
    height: 100px;
    margin-bottom: 0.8rem;
  }

  section {
    padding: 30px 10px;
  }

  .project-card {
    padding: 15px;
  }

  nav a {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading animation */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Tech stack tags */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: rgba(0, 173, 181, 0.2);
  color: #00adb5;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(0, 173, 181, 0.3);
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: rgba(0, 173, 181, 0.3);
  transform: scale(1.05);
}

/* Project images */
.project-card img {
  transition: transform 0.3s ease;
}

.project-card:hover img {
  transform: scale(1.05);
}

/* Scroll indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(0, 173, 181, 0.2);
  z-index: 1000;
}

.scroll-progress {
  height: 100%;
  background: linear-gradient(90deg, #00adb5, #00d4aa);
  width: 0%;
  transition: width 0.1s ease;
}

/* Mobile menu toggle (for future enhancement) */
.menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 10px;
}

.menu-toggle span {
  width: 25px;
  height: 3px;
  background: #00adb5;
  margin: 3px 0;
  transition: 0.3s;
}

@media (max-width: 768px) {
  .tech-stack {
    justify-content: center;
  }
  
  .tech-tag {
    font-size: 0.7rem;
    padding: 3px 8px;
  }
}
